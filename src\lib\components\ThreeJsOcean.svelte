<script lang="ts">
  import * as THREE from 'three'
  //   import Stats from 'three/examples/jsm/libs/stats.module'
  //   import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min'
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
  import { Water } from 'three/examples/jsm/objects/Water'
  import { Sky } from 'three/examples/jsm/objects/Sky'
  import { onMount } from 'svelte'

  onMount(() => {
    let container
    // let stats
    let camera, scene, renderer
    let controls, water, sun, mesh

    init()

    function init() {
      container = document.getElementById('container')

      //

      renderer = new THREE.WebGLRenderer()
      renderer.setPixelRatio(window.devicePixelRatio)
      renderer.setSize(window.innerWidth, window.innerHeight)
      renderer.setAnimationLoop(animate)
      renderer.toneMapping = THREE.ACESFilmicToneMapping
      //   renderer.toneMappingExposure = 0.5
      renderer.toneMappingExposure = 0.75
      container.appendChild(renderer.domElement)

      //

      scene = new THREE.Scene()

      camera = new THREE.PerspectiveCamera(55, window.innerWidth / window.innerHeight, 1, 20000)
      camera.position.set(30, 30, 100)

      //

      sun = new THREE.Vector3()

      // Water

      const waterGeometry = new THREE.PlaneGeometry(10000, 10000)

      water = new Water(waterGeometry, {
        textureWidth: 512,
        textureHeight: 512,
        waterNormals: new THREE.TextureLoader().load('textures/waternormals.jpg', function (texture) {
          texture.wrapS = texture.wrapT = THREE.RepeatWrapping
        }),
        sunDirection: new THREE.Vector3(),
        // sunColor: 0xffffff,
        sunColor: 0xff000000,
        // waterColor: 0x001e0f,
        waterColor: 0x1f0ff,
        distortionScale: 3.7,
        fog: scene.fog !== undefined
      })

      water.rotation.x = -Math.PI / 2

      scene.add(water)

      // Skybox

      const sky = new Sky()
      sky.scale.setScalar(10000)
      scene.add(sky)

      const skyUniforms = sky.material.uniforms

      //   skyUniforms['turbidity'].value = 10
      skyUniforms['turbidity'].value = 3
      //   skyUniforms['rayleigh'].value = 2
      skyUniforms['rayleigh'].value = 1
      //   skyUniforms['mieCoefficient'].value = 0.005
      skyUniforms['mieCoefficient'].value = 0.0001
      //   skyUniforms['mieDirectionalG'].value = 0.8

      const parameters = {
        elevation: 2,
        azimuth: 180
      }

      const pmremGenerator = new THREE.PMREMGenerator(renderer)
      const sceneEnv = new THREE.Scene()

      let renderTarget

      function updateSun() {
        const phi = THREE.MathUtils.degToRad(90 - parameters.elevation)
        const theta = THREE.MathUtils.degToRad(parameters.azimuth)

        sun.setFromSphericalCoords(1, phi, theta)

        sky.material.uniforms['sunPosition'].value.copy(sun)
        water.material.uniforms['sunDirection'].value.copy(sun).normalize()

        if (renderTarget !== undefined) renderTarget.dispose()

        sceneEnv.add(sky)
        renderTarget = pmremGenerator.fromScene(sceneEnv)
        scene.add(sky)

        scene.environment = renderTarget.texture
      }

      updateSun()

      //

      const geometry = new THREE.BoxGeometry(30, 30, 30)
      //   const material = new THREE.MeshStandardMaterial({ roughness: 0 })
      const material = new THREE.MeshStandardMaterial({ color: 0x00dfff, roughness: 0 })

      mesh = new THREE.Mesh(geometry, material)
      scene.add(mesh)

      //

      controls = new OrbitControls(camera, renderer.domElement)
      controls.maxPolarAngle = Math.PI * 0.495
      // controls.target.set(0, 10, 0)
      //   controls.target.set(0, 100, 0)
      controls.minDistance = 40.0
      controls.maxDistance = 200.0
      controls.update()

      //
      //   stats = new Stats()
      //   container.appendChild(stats.dom)

      // GUI

      //   const gui = new GUI()

      //   const folderSky = gui.addFolder('Sky')
      //   folderSky.add(parameters, 'elevation', 0, 90, 0.1).onChange(updateSun)
      //   folderSky.add(parameters, 'azimuth', -180, 180, 0.1).onChange(updateSun)
      //   folderSky.open()

      //   const waterUniforms = water.material.uniforms

      //   const folderWater = gui.addFolder('Water')
      //   folderWater.add(waterUniforms.distortionScale, 'value', 0, 8, 0.1).name('distortionScale')
      //   folderWater.add(waterUniforms.size, 'value', 0.1, 10, 0.1).name('size')
      //   folderWater.open()

      //

      window.addEventListener('resize', onWindowResize)
    }

    function onWindowResize() {
      camera.aspect = window.innerWidth / window.innerHeight
      camera.updateProjectionMatrix()

      renderer.setSize(window.innerWidth, window.innerHeight)
    }

    function animate() {
      render()
      //   stats.update()
    }

    function render() {
      const time = performance.now() * 0.001

      mesh.position.y = Math.sin(time) * 20 + 5
      mesh.rotation.x = time * 0.5
      mesh.rotation.z = time * 0.51

      water.material.uniforms['time'].value += 1.0 / 60.0

      renderer.render(scene, camera)
    }
  })
</script>

<div class="ocean-wrapper">
  <div id="container"></div>
  <!-- <div id="info">
    <a href="https://threejs.org" target="_blank" rel="noopener">three.js</a> - webgl ocean
  </div> -->
</div>

<style>
  .ocean-wrapper {
    height: 100vh;
    width: 100vw;
  }
</style>
