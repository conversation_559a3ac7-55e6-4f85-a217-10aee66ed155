
/**
 * Returns a date string in the format "YYYY MONTH DD"
 * @param dateInput - Millisecond epoch timestamp or ISO date string
 * @returns String formatted as "2025 JUNE 03"
 */
export function getDate(dateInput: string | number): string {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : new Date(dateInput)

  if (isNaN(date.getTime())) {
    return 'Invalid date'
  }

  const year = date.getFullYear()
  const month = date.toLocaleString('en-US', { month: 'long' }).toUpperCase()
  const day = date.getDate().toString().padStart(2, '0')

  return `${year} ${month} ${day}`
}
export function getTimeElapsed(dateInput: string | number): string {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : new Date(dateInput)
  const now = new Date()

  if (isNaN(date.getTime())) {
    return 'Invalid date'
  }

  let seconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  // Time units in seconds
  const timeUnits = [
    { name: 'y', seconds: 31536000 },
    { name: 'm', seconds: 2592000 },
    { name: 'd', seconds: 86400 },
    { name: 'h', seconds: 3600 },
    { name: 'm', seconds: 60 },
    { name: 's', seconds: 1 }
  ]

  // Calculate values for each unit with modulus
  const values = []
  for (let i = 0; i < timeUnits.length; i++) {
    const unit = timeUnits[i]
    const value = Math.floor(seconds / unit.seconds)

    if (value > 0) {
      values.push({
        value,
        name: unit.name,
        plural: value !== 1
      })

      // Subtract the time accounted for and continue with remainder
      seconds = seconds % unit.seconds
    }
  }

  // Take only the two most significant units
  const significantValues = values.slice(0, 2)

  // If no significant values, return "just now"
  if (significantValues.length === 0) {
    return 'just now'
  }

  // Format the result
  return significantValues
    .map(v => `${v.value}${v.name}${v.plural ? '' : ''}`)
    .join(' ')
}


