<script lang="ts">
	import { superForm, defaults } from 'sveltekit-superforms'
	import type { Infer } from 'sveltekit-superforms'
	import SuperDebug from 'sveltekit-superforms'
	import { zod } from 'sveltekit-superforms/adapters'
	import { Button } from '@/lib/components/ui/button/index.js'
	import * as Dialog from '@/lib/components/ui/dialog/index.js'
	import { Input } from '@/lib/components/ui/input/index.js'
	// import ScrollArea from '@/lib/components/ui/scroll-area/scroll-area.svelte'
	import { Label } from '@/lib/components/ui/label/index.js'
	import { formSchema } from './schema'
	import { postApiData } from '@/lib/helpers/httpWrappers.js'
	import { REEFDOJO_API } from '@/lib/helpers/constants'
	import FormWrapper from '../FormWrapper.svelte'
	import * as Icon from '@lucide/svelte/icons/index'
	import { loadUserSession } from '@/lib/helpers/auth'
	import { isEmpty } from '@/lib/helpers/functions'
	import { onMount } from 'svelte'
	import TermsAndConditions from '../../TermsAndConditions.svelte'

	const data = defaults(zod(formSchema))

	const emailInputId = 'email-input-id'

	let errorMessage = $state('')
	let successMessage = $state('')
	let isSubmitting = $state(false)

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const { form, errors, message, enhance, delayed } = superForm<
		Infer<typeof formSchema>,
		{ status: number; text: string } // Strongly typed status message
	>(data, {
		SPA: true,
		resetForm: false,
		clearOnSubmit: 'errors-and-message',
		validators: zod(formSchema),
		async onUpdate({ form }) {
			if (!form.valid) return
			try {
				isSubmitting = true
				const apiResults = await postApiData(`${REEFDOJO_API}auth/login`, form.data, true)

				// form.message = { status: 200, text: `${name}, brought to you by randomuser.me` }
				successMessage = apiResults.data.message

				await loadUserSession()
			} catch (e) {
				console.error(e.message)
				form.message = { status: 500, text: `Error loggin in.` }
			} finally {
				isSubmitting = false
			}
		}
	})

	onMount(() => {
		const emailInputEl = document.querySelector(`#${emailInputId}`)
		emailInputEl?.focus()
	})
</script>

{#if false}
	<SuperDebug data={$form} />
{/if}

<FormWrapper title="LOGIN" description="Please log in below." {body} {footer} />

{#snippet body()}
	<form method="POST" use:enhance>
		{#if errorMessage}
			<div class="mb-4 text-sm font-bold break-words text-red-500">
				{errorMessage}
			</div>
		{/if}

		{#if successMessage}
			<div class="mb-4 text-sm font-bold break-words text-green-500">
				{successMessage}
			</div>
		{/if}

		<div class="mb-4">
			<Label for="email">Email</Label>
			<Input
				id={emailInputId}
				name="email"
				type="email"
				autocomplete="email"
				bind:value={$form.email}
				aria-invalid={$errors.email ? 'true' : undefined}
			/>
			{#if $errors.email}
				<p class="mt-1 text-sm font-bold break-words text-red-500">
					{#each $errors.email as err, i (i)}
						{err} <br />
					{/each}
				</p>
			{/if}
		</div>

		<div class="mb-4">
			<Label for="password">Password</Label>
			<Input
				id="password"
				name="password"
				type="password"
				autocomplete="current-password"
				bind:value={$form.password}
				aria-invalid={$errors.password ? 'true' : undefined}
			/>
			{#if $errors.password}
				<p class="mt-1 text-sm font-bold break-words text-red-500">
					{#each $errors.password as err, i (i)}
						{err} <br />
					{/each}
				</p>
			{/if}
		</div>

		<div class="mt-4 flex justify-end">
			<Button
				type="submit"
				disabled={isSubmitting ||
					!isEmpty($errors) ||
					isEmpty($form.password) ||
					isEmpty($form.email)}
			>
				{#if isSubmitting}
					<Icon.LoaderCircle class="mr-2 h-4 w-4 animate-spin" />
				{:else}
					<span>Submit</span>
				{/if}
			</Button>
		</div>
	</form>
{/snippet}

{#snippet footer()}
	<!-- <p>
    Forgot your
    <a href="/login/password-reset" class="underline underline-offset-4 hover:text-primary"> password? </a>
  </p> -->
	<p class="text-xs">
		By clicking Submit, you agree to our
		<Dialog.Root>
			<Dialog.Trigger>
				<!-- <a href="/terms" target="_blank" class="hover:text-primary underline underline-offset-4"> -->
				<!-- <a href="/terms" target="_blank" class="hover:text-primary underline underline-offset-4"> -->
				<a class="text-primary hover:text-primary/80 cursor-pointer underline underline-offset-4">
					Terms of Service
				</a>
			</Dialog.Trigger>
			<Dialog.Content class="h-[80vh] max-w-[50vw]!">
				<div class="relative overflow-y-auto">
					<TermsAndConditions />
				</div>
				<!-- <Dialog.Header>
					<Dialog.Title>Are you sure absolutely sure?</Dialog.Title>
					<Dialog.Description>
						This action cannot be undone. This will permanently delete your account and remove your
						data from our servers.
					</Dialog.Description>
				</Dialog.Header> -->
			</Dialog.Content>
		</Dialog.Root>

		<!-- and
    <a href="/privacy" target="_blank" class="underline underline-offset-4 hover:text-primary">
      Privacy Policy
    </a> -->
		.
	</p>
{/snippet}
