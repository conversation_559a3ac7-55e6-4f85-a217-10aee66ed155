<script lang="ts">
	import type { FeedPostT } from '@/lib/components/ui/feed/posts/types'
	import { getTimeElapsed } from '@/lib/helpers/time'
	import FeedPostReply from '@/lib/components/ui/feed/posts/components/FeedPostReply.svelte'

	let {
		userAvatarUrl = '',
		username = '',
		content = '',
		dateCreated = '',
		userId = ''
	}: FeedPostT = $props()
</script>

<div
	class="bg-muted border-border flex h-full w-full flex-col overflow-hidden rounded-lg border shadow-sm dark:bg-slate-800"
>
	<div class="m-4 flex">
		<div class="mr-4 flex shrink-0 flex-col items-center">
			<img
				src={userAvatarUrl}
				alt="User avatar"
				class="border-primary mb-2 size-8 rounded-full border-2 object-cover drop-shadow-xl"
			/>
			<h3 class="text-card-foreground text-xs font-medium">{username}</h3>
		</div>

		<div class="min-w-0 flex-1">
			<p class="text-card-foreground text-sm break-words">
				{content}
			</p>
		</div>

		<div class="ml-4 flex shrink-0 flex-col justify-between self-stretch">
			<div class="text-muted-foreground">
				<p class="text-xs">{getTimeElapsed(dateCreated)}</p>
			</div>
		</div>
	</div>
	<FeedPostReply isOutline={true} {userId} />
</div>
